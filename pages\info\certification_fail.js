// pages/info/certification_fail.js
import util from '../../utils/util';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0,  // 状态栏高度
    failReason: '',      // 失败原因
    rejectTime: '',      // 拒绝时间
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 获取失败原因和时间（从页面参数或缓存中获取）
    if (options.reason) {
      this.setData({
        failReason: decodeURIComponent(options.reason)
      });
    }
    
    // 支持review_remark字段
    if (options.review_remark) {
      this.setData({
        failReason: decodeURIComponent(options.review_remark)
      });
    }

    if (options.time) {
      this.setData({
        rejectTime: decodeURIComponent(options.time)
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 返回上一页
   */
  navigateBack() {
    wx.navigateBack();
  },

  /**
   * 重新认证
   */
  reApply() {
    wx.redirectTo({
      url: '/pages/info/certification'
    });
  },

  /**
   * 联系客服
   */
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '如有疑问，请联系客服：400-123-4567',
      showCancel: false,
      confirmText: '知道了'
    });
  }
})