// pages/info/certification.js
import api from '../../utils/api';
import util from '../../utils/util';
import config from '../../config';  // 假设配置文件在这个位置
import cosUpload from '../../utils/cos-upload';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0,  // 状态栏高度
    isAgree: false,  // 是否同意协议
    licenseImage: '',  // 营业执照图片
    licenseImageBase64: '', // 营业执照图片base64数据
    handbookImage: '', // 企业手册图片
    pdfPath: '',      // PDF文件路径
    pdfName: '',      // PDF文件名
    pdfSize: '',      // PDF文件大小
    companyName: null,   // 公司名称
    address: null,       // 地址
    registeredCapital: null, // 注册资金
    companyIntro: null,    // 企业介绍
    brandInfo: null,        // 主营品牌和车型
    userInfo: null,        // 用户信息
    isFirstLoad: true,     // 标记首次加载
    status: 0,             // 认证状态
    showSuccessModal: false, // 控制成功模态框显示
    isApproved: false      // 是否已认证通过（status === 2）
  },

  // 添加基础URL常量
  // baseUrl: 'https://zhaochexia-**********.cos.ap-guangzhou.myqcloud.com/uploads/',
  baseUrl: config.COS_CONFIG.url + config.COS_CONFIG.path,

  // 获取文件路径后缀（用于提交）
  getFilePathSuffix(fullPath) {
    if (!fullPath) return '';
    // 检查是否已经包含完整路径
    if (fullPath.startsWith('http')) {
      // 从URL中提取路径
      const parts = fullPath.split(this.baseUrl);
      if (parts.length > 1) {
        // 确保路径以 /uploads 开头
        return '/uploads/' + parts[1];
      }
      return '';
    } else if (fullPath.includes('uploads/')) {
      // 如果已经包含 uploads/ 但不是完整URL
      const parts = fullPath.split('uploads/');
      return '/uploads/' + parts[1];
    } else {
      // 如果是相对路径，直接添加前缀
      return '/uploads/' + fullPath;
    }
  },

  // 获取完整文件URL（用于显示）
  getFullFileUrl(pathSuffix) {
    if (!pathSuffix) return '';
    // 移除开头的 /uploads 如果存在
    const cleanPath = pathSuffix.startsWith('/uploads/')
      ? pathSuffix.substring(9)
      : (pathSuffix.startsWith('uploads/') ? pathSuffix.substring(8) : pathSuffix);
    return this.baseUrl + cleanPath;
  },

  // 将图片转换为base64
  imageToBase64(filePath) {
    return new Promise((resolve, reject) => {
      wx.getFileSystemManager().readFile({
        filePath: filePath,
        encoding: 'base64',
        success: (res) => {
          resolve(res.data);
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  },

  observers: {
    'companyName, address, registeredCapital, licenseImage, handbookImage, companyIntro, brandInfo': function (
      companyName,
      address,
      registeredCapital,
      licenseImage,
      handbookImage,
      companyIntro,
      brandInfo
    ) {
      // 数据变化监听（已移除调试输出）
    }
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight || 20
    });

    this.data.isFirstLoad = true;  // 标记首次加载
    const userInfo = util.getUserInfo();
    // 先设置userInfo，确保页面有初始数据
    this.setData({ userInfo });

    if (userInfo && userInfo.app_id) {
      // 显示加载中
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      // 获取认证信息
      api.user.getCertificationStatus({ app_id: userInfo.app_id })
        .then(result => {
          wx.hideLoading();
          this.data.isFirstLoad = false;  // 重置首次加载标记

          if (result && result.has_certification !== undefined) {
            const authInfo = result;

            setTimeout(() => {
              const currentStatus = authInfo.status || 0;
              this.setData({
                companyName: authInfo.company_name || '',
                address: authInfo.address || '',
                registeredCapital: authInfo.registration_fee || '',
                licenseImage: this.getFullFileUrl(authInfo.business_license),
                handbookImage: this.getFullFileUrl(authInfo.handbook_image),
                companyIntro: authInfo.introduce || '',
                brandInfo: authInfo.brand_info || '',
                pdfPath: this.getFullFileUrl(authInfo.pdf_file?.path),
                pdfName: authInfo.pdf_file?.name || '',
                pdfSize: authInfo.pdf_file?.size || '',
                status: currentStatus,
                isApproved: currentStatus === 2  // 认证通过时设置为true
              });
              
              // 如果认证已通过，显示成功模态框
              if (currentStatus === 2) {
                this.showSuccessModal();
              }
            }, 100);
          }

          // 如果已经认证通过，显示提示
          // if (authInfo.is_auth === 2) {
          //   wx.showToast({
          //     title: '您的企业已认证通过',
          //     icon: 'success',
          //     duration: 2000
          //   });
          // }
          // // 如果正在审核中
          // else if (authInfo.is_auth === 1) {
          //   wx.showToast({
          //     title: '认证信息审核中',
          //     icon: 'none',
          //     duration: 2000
          //   });
          // }
        })
        .catch(error => {
          wx.hideLoading();
          this.data.isFirstLoad = false;  // 重置首次加载标记
          wx.showToast({
            title: '获取认证信息失败',
            icon: 'none',
            duration: 2000
          });
        });
    } else {
      this.setData({ userInfo }); // 如果没有app_id，也要设置userInfo
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 确保状态栏高度正确设置
    if (!this.data.statusBarHeight) {
      const systemInfo = wx.getSystemInfoSync();
      this.setData({
        statusBarHeight: systemInfo.statusBarHeight || 20
      });
    }

    // 页面显示时也尝试获取数据
    const userInfo = util.getUserInfo();
    if (userInfo && userInfo.app_id && !this.data.companyName && !this.data.isFirstLoad) {
      this.fetchAuthInfo(userInfo.app_id);
    }
    // 检查数据加载状态
    this.checkDataStatus();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },



  // 检查OCR使用次数并决定是否进行智能识别
  checkOCRUsageAndRecognize(imageUrl, base64Data) {
    const userInfo = util.getUserInfo();
    if (!userInfo || !userInfo.app_id) {
      wx.hideLoading();
      wx.showToast({
        title: '上传成功',
        icon: 'success',
        duration: 2000
      });
      return;
    }

    // 更新加载提示
    wx.showLoading({
      title: '检查智能识别...',
      mask: true
    });

    // 调用查询使用次数接口
    api.user.getAppUsage({ app_id: userInfo.app_id })
      .then(result => {
        console.log('OCR使用次数查询结果:', result);
        
        // 判断返回数据格式：可能是直接数据或包装格式
        let usageData = null;
        if (result && result.app_id && result.total_limit !== undefined) {
          // 直接数据格式：{app_id: "1142", total_limit: 10, used_count: 9, ...}
          usageData = result;
        } else if (result && (result.code === 1 || result.code === "1") && result.data) {
          // 包装格式：{code: 1, data: {...}}
          usageData = result.data;
        }
        
        if (usageData) {
          const { used_count, total_limit, can_use } = usageData;
          
          // 检查是否超过使用次数限制
          if (!can_use || used_count >= total_limit) {
            // 超过次数限制，只显示上传成功，不进行OCR识别
            wx.hideLoading();
            wx.showToast({
              title: '上传成功',
              icon: 'success',
              duration: 2000
            });
            
            // 延迟显示智能识别次数用完的提示
            setTimeout(() => {
              wx.showToast({
                title: `智能识别次数已用完（${used_count}/${total_limit}），请手动填写信息`,
                icon: 'none',
                duration: 3000
              });
            }, 2500);
          } else {
            // 还有剩余次数，进行OCR识别
            wx.showToast({
              title: `剩余智能识别次数：${total_limit - used_count}次`,
              icon: 'none',
              duration: 1500
            });
            
            // 继续进行OCR识别
            this.recognizeLicense(imageUrl, base64Data);
          }
        } else {
          // 如果查询失败，只显示上传成功，不进行OCR识别
          wx.hideLoading();
          wx.showToast({
            title: '上传成功',
            icon: 'success',
            duration: 2000
          });
          console.warn('查询OCR使用次数失败，跳过智能识别');
        }
      })
      .catch(error => {
        // 查询失败时，只显示上传成功，不进行OCR识别
        wx.hideLoading();
        wx.showToast({
          title: '上传成功',
          icon: 'success',
          duration: 2000
        });
        console.error('查询OCR使用次数失败:', error);
      });
  },

  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        
        // 先检查图片文件大小
        wx.getFileInfo({
          filePath: tempFilePath,
          success: (fileInfo) => {
            const fileSizeInMB = fileInfo.size / (1024 * 1024);
            console.log('图片原始大小:', fileSizeInMB.toFixed(2) + 'MB');
            
            // Base64编码会增加约33%的大小，所以原始文件建议不超过5M
            if (fileSizeInMB > 5) {
              wx.showToast({
                title: `图片过大(${fileSizeInMB.toFixed(1)}MB)，请选择5MB以下的图片`,
                icon: 'none',
                duration: 3000
              });
              return;
            }
            
            // 显示加载提示
            wx.showLoading({
              title: '处理中...',
              mask: true
            });

            // 同时进行图片转base64和上传COS
            Promise.all([
              this.imageToBase64(tempFilePath),
              cosUpload.uploadFile(tempFilePath, 'license')
            ])
              .then(([base64Data, uploadResult]) => {
                // 检查Base64编码后的大小
                const base64SizeInMB = (base64Data.length * 3/4) / (1024 * 1024);
                console.log('Base64编码后大小:', base64SizeInMB.toFixed(2) + 'MB');
                
                if (base64SizeInMB > 7) {
                  wx.hideLoading();
                  wx.showToast({
                    title: `图片编码后过大(${base64SizeInMB.toFixed(1)}MB)，请选择更小的图片`,
                    icon: 'none',
                    duration: 3000
                  });
                  return;
                }
                
                const imageUrl = this.getFullFileUrl(this.getFilePathSuffix(uploadResult.url));
                this.setData({
                  licenseImage: imageUrl,
                  licenseImageBase64: base64Data  // 保存base64数据
                });
                
                // 检查OCR使用次数后决定是否进行智能识别
                this.checkOCRUsageAndRecognize(imageUrl, base64Data);
              })
              .catch(error => {
                wx.hideLoading();
                
                if (error.message && error.message.includes('上传')) {
                  wx.showToast({
                    title: '上传失败',
                    icon: 'none'
                  });
                } else if (error.message && error.message.includes('过大')) {
                  wx.showToast({
                    title: error.message,
                    icon: 'none',
                    duration: 3000
                  });
                } else {
                  wx.showToast({
                    title: '图片处理失败',
                    icon: 'none'
                  });
                }
              });
          },
          fail: (error) => {
            console.error('获取文件信息失败:', error);
            wx.showToast({
              title: '图片信息获取失败',
              icon: 'none'
            });
          }
        });
      }
    });
  },

  // 删除图片
  deleteLicenseImage() {
    this.setData({
      licenseImage: '',
      licenseImageBase64: ''  // 同时清除base64数据
    });
  },



  // 营业执照OCR识别
  recognizeLicense(imageUrl, base64Data = null) {
    const userInfo = util.getUserInfo();
    if (!userInfo || !userInfo.app_id) {
      wx.hideLoading();
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      });
      return;
    }

    // 更新加载提示文案
    wx.showLoading({
      title: '智能识别中...',
      mask: true
    });

    // 构建请求参数，优先使用base64
    const requestData = {
      app_id: userInfo.app_id
    };

    if (base64Data) {
      // 优先使用base64数据
      requestData.image_base64 = base64Data;
    } else {
      // 没有base64则使用图片URL
      requestData.image_url = imageUrl;
    }

    // 调用OCR识别接口
    api.user.bizLicenseOCR(requestData)
      .then(result => {
        console.log('OCR接口返回:', result);
        
        // 优先检查是否是错误响应
        if (result && result.code === 0) {
          // 检查是否包含"不是营业执照"的错误信息
          const errorMsg = result.msg || '';
          const isNotLicenseError = errorMsg.includes('不是营业执照') ||
                                   errorMsg.includes('非营业执照') ||
                                   errorMsg.includes('无法识别为营业执照');

          if (isNotLicenseError) {
            // 如果是营业执照相关错误，提示用户重新上传
            wx.showToast({
              title: '请上传清晰的营业执照图片',
              icon: 'none',
              duration: 3000
            });

            // 清除已上传的图片
            this.setData({
              licenseImage: '',
              licenseImageBase64: ''
            });
          } else {
            // 其他OCR识别失败，统一提示手动输入
            wx.showToast({
              title: '上传成功，请手动填写信息',
              icon: 'none',
              duration: 3000
            });
          }
          return;
        }
        
        // 检查数据格式：可能是直接返回数据对象，也可能是包装格式
        let ocrData = null;
        if (result && result.code === 1 && result.data) {
          // 标准包装格式：{code: 1, data: {...}}
          ocrData = result.data;
        } else if (result && result.name) {
          // 直接数据格式：{name: "...", address: "...", ...}
          ocrData = result;
        }
        
        if (ocrData) {
          console.log('OCR识别数据:', ocrData);
          
          // 将识别结果填充到表单中
          const updateData = {};
          
          // 强制赋值测试 - 不检查条件，直接赋值
          if (ocrData.name) {
            updateData.companyName = ocrData.name;
          }
          
          if (ocrData.address) {
            updateData.address = ocrData.address;
          }
          
          // 注册资本 (capital字段，提取数字部分)
          if (ocrData.capital) {
            let capitalValue = ocrData.capital;
            
            // 提取注册资本中的数字部分，支持中文数字和阿拉伯数字
            const arabicMatch = capitalValue.match(/[\d.]+/);
            if (arabicMatch) {
              // 如果包含阿拉伯数字，直接使用
              updateData.registeredCapital = arabicMatch[0];
            } else {
              // 简单处理几种常见情况
              if (capitalValue.includes('万')) {
                if (capitalValue.includes('柒仟') || capitalValue.includes('七千')) {
                  updateData.registeredCapital = '7000';
                } else if (capitalValue.includes('壹万') || capitalValue.includes('一万')) {
                  updateData.registeredCapital = '10000';
                } else {
                  // 其他情况保持原文本，让用户手动修改
                  updateData.registeredCapital = capitalValue.replace(/人民币|元整|元|万元/g, '');
                }
              } else {
                // 移除常见的货币单位文字
                updateData.registeredCapital = capitalValue.replace(/人民币|元整|元/g, '');
              }
            }
          }
          
          console.log('准备更新的数据:', updateData);
          
          // 更新页面数据
          if (Object.keys(updateData).length > 0) {
            this.setData(updateData);
            
            wx.showToast({
              title: `识别成功，已自动填充${Object.keys(updateData).length}项信息`,
              icon: 'success',
              duration: 3000
            });
          } else {
            wx.showToast({
              title: '识别成功',
              icon: 'success',
              duration: 2000
            });
          }
          

        } else {
          // 识别失败但上传成功，统一提示手动填写信息
          wx.showToast({
            title: '上传成功，请手动填写信息',
            icon: 'none',
            duration: 3000
          });
        }
      })
      .catch(error => {
        // 检查是否包含"不是营业执照"的错误信息
        const errorMsg = error.message || '';
        const isNotLicenseError = errorMsg.includes('不是营业执照') ||
                                 errorMsg.includes('非营业执照') ||
                                 errorMsg.includes('无法识别为营业执照');

        if (isNotLicenseError) {
          // 如果是营业执照相关错误，提示用户重新上传
          wx.showToast({
            title: '请上传清晰的营业执照图片',
            icon: 'none',
            duration: 3000
          });

          // 清除已上传的图片
          this.setData({
            licenseImage: '',
            licenseImageBase64: ''
          });
        } else {
          // 其他OCR失败不影响上传成功，统一提示手动输入
          wx.showToast({
            title: '上传成功，请手动填写信息',
            icon: 'none',
            duration: 3000
          });
        }

        // 记录错误日志但不向用户显示具体错误
        console.error('OCR识别接口调用失败:', error);
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 输入事件处理
  onCompanyNameInput(e) {
    this.setData({
      companyName: e.detail.value
    });
  },

  onAddressInput(e) {
    this.setData({
      address: e.detail.value
    });
  },

  onRegisteredCapitalInput(e) {
    this.setData({
      registeredCapital: e.detail.value
    });
  },



  // 选择企业手册图片
  chooseHandbookImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 显示加载提示
        wx.showLoading({
          title: '上传中...',
          mask: true
        });

        // 上传图片到腾讯云COS
        cosUpload.uploadFile(res.tempFilePaths[0], 'handbook')
          .then(result => {
            this.setData({
              handbookImage: this.getFullFileUrl(this.getFilePathSuffix(result.url))
            });
            wx.hideLoading();
          })
          .catch(error => {
            console.error('上传失败:', error);
            wx.hideLoading();
            wx.showToast({
              title: '上传失败',
              icon: 'none'
            });
          });
      }
    });
  },

  // 删除企业手册图片
  deleteHandbookImage() {
    this.setData({
      handbookImage: ''
    });
  },

  // 选择PDF文件
  choosePdf() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      extension: ['pdf'],
      success: (res) => {
        const file = res.tempFiles[0];

        // 检查文件类型
        if (!file.name.toLowerCase().endsWith('.pdf')) {
          wx.showToast({
            title: '请选择PDF文件',
            icon: 'none'
          });
          return;
        }

        // 显示加载提示
        wx.showLoading({
          title: '上传中...',
          mask: true
        });

        // 上传PDF到腾讯云COS
        cosUpload.uploadFile(file.path, 'pdf')
          .then(result => {
            this.setData({
              pdfPath: this.getFullFileUrl(this.getFilePathSuffix(result.url)),
              pdfName: file.name,
              pdfSize: (file.size / 1024 / 1024).toFixed(2) + 'MB'
            });
            wx.hideLoading();
          })
          .catch(error => {
            console.error('上传失败:', error);
            wx.hideLoading();
            wx.showToast({
              title: '上传失败',
              icon: 'none'
            });
          });
      }
    });
  },

  // 删除PDF
  deletePdf() {
    this.setData({
      pdfPath: '',
      pdfName: '',
      pdfSize: ''
    });
  },

  // 预览PDF文件
  previewPdf() {
    if (!this.data.pdfPath) {
      wx.showToast({
        title: '没有可预览的PDF文件',
        icon: 'none'
      });
      return;
    }

    // 获取完整的PDF文件路径
    let fullPdfPath = '';
    if (this.data.pdfPath.startsWith('http')) {
      fullPdfPath = this.data.pdfPath;
    } else {
      // 移除开头的 /uploads 如果存在
      const cleanPath = this.data.pdfPath.startsWith('/uploads/')
        ? this.data.pdfPath.substring(9)
        : (this.data.pdfPath.startsWith('uploads/') ? this.data.pdfPath.substring(8) : this.data.pdfPath);
      fullPdfPath = this.baseUrl + cleanPath;
    }



    wx.showLoading({
      title: '加载中...',
    });

    // 先下载文件到本地临时路径
    wx.downloadFile({
      url: fullPdfPath,
      success: (res) => {
        if (res.statusCode === 200) {
          // 使用微信的文档预览API
          wx.openDocument({
            filePath: res.tempFilePath,
            fileType: 'pdf',
            success: function (res) {
              // 文档打开成功
            },
            fail: function (error) {
              wx.showToast({
                title: '文档打开失败',
                icon: 'none'
              });
            }
          });
        } else {
          wx.showToast({
            title: '文件下载失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        wx.showToast({
          title: '文件下载失败',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },



  // 企业介绍输入处理
  onCompanyIntroInput(e) {
    this.setData({
      companyIntro: e.detail.value
    });
  },



  // 主营品牌输入处理
  onBrandInfoInput(e) {
    this.setData({
      brandInfo: e.detail.value
    });
  },

  // 切换协议同意状态
  toggleAgree() {
    this.setData({
      isAgree: !this.data.isAgree
    });
  },

  // 查看协议
  viewAgreement(e) {
    const type = e.currentTarget.dataset.type;
    let url = '';

    if (type === 'service') {
      url = '/pages/agreement/service';
    } else if (type === 'privacy') {
      url = '/pages/agreement/privacy';
    }

    wx.navigateTo({
      url: url
    });
  },

  // 提交信息
  submitInfo() {
    if (!this.data.isAgree) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      });
      return;
    }

    // 验证表单是否填写完整
    if (!this.data.licenseImage) {
      wx.showToast({
        title: '请上传营业执照',
        icon: 'none'
      });
      return;
    }

    if (!this.data.companyName) {
      wx.showToast({
        title: '请输入公司全称',
        icon: 'none'
      });
      return;
    }

    if (!this.data.address) {
      wx.showToast({
        title: '请输入公司地址',
        icon: 'none'
      });
      return;
    }

    if (!this.data.registeredCapital) {
      wx.showToast({
        title: '请输入注册金',
        icon: 'none'
      });
      return;
    }

    // 企业手册验证（可选，根据需求决定是否必填）
    if (!this.data.handbookImage) {
      wx.showToast({
        title: '请上传企业手册图片',
        icon: 'none'
      });
      return;
    }

    // 企业介绍验证
    if (!this.data.companyIntro) {
      wx.showToast({
        title: '请输入企业详细介绍',
        icon: 'none'
      });
      return;
    }

    // 主营品牌验证
    if (!this.data.brandInfo) {
      wx.showToast({
        title: '请输入主营品牌和车型',
        icon: 'none'
      });
      return;
    }

    // 收集表单数据
    const formData = {
      // 营业执照信息
      business_license: this.getFilePathSuffix(this.data.licenseImage), //只传后半段路径
      company_name: this.data.companyName,//公司全称
      address: this.data.address,//地址
      registration_fee: this.data.registeredCapital,//注册金

      // 企业手册信息
      handbookImage: this.getFilePathSuffix(this.data.handbookImage),
      pdfFile: {
        path: this.getFilePathSuffix(this.data.pdfPath),
        name: this.data.pdfName,
        size: this.data.pdfSize
      },

      // 企业详细介绍
      introduce: this.data.companyIntro, //企业介绍

      // 主营品牌和车型
      brandInfo: this.data.brandInfo,

      // 获取用户app_id
      app_id: util.getUserInfo()?.app_id || ''
    };



    // 显示加载提示
    wx.showLoading({
      title: '提交中...',
      mask: true
    });

    // 提交数据到服务器
    api.user.submitCertification(formData)
      .then(result => {
        wx.hideLoading();

        // 更新本地状态为待审核
        this.setData({
          status: 1, // 标记为待审核状态
          isApproved: false // 待审核状态不是认证通过
        });

        // 提示用户提交成功
        wx.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 2000
        });

        // 延迟跳转到认证审核中页面
        setTimeout(() => {
          wx.redirectTo({
            url: '/pages/info/certification_review'
          });
        }, 2000);
      })
      .catch(error => {
        wx.hideLoading();
        wx.showToast({
          title: error.message || '提交失败，请重试',
          icon: 'none',
          duration: 2000
        });
      });
  },

  // 显示成功模态框
  showSuccessModal() {
    this.setData({
      showSuccessModal: true
    });
    
    // 3秒后自动关闭
    setTimeout(() => {
      this.hideSuccessModal();
    }, 3000);
  },

  // 隐藏成功模态框
  hideSuccessModal() {
    this.setData({
      showSuccessModal: false
    });
  },

  // 抽取获取认证信息的方法
  fetchAuthInfo(app_id) {
    api.user.getCertificationStatus({ app_id })
      .then(result => {
        if (result.code === 0 && result.data) {
          const authInfo = result.data;
          this.setData({
            companyName: authInfo.company_name || '',
            address: authInfo.address || '',
            registeredCapital: authInfo.registration_fee || '',
            // ... 其他数据设置
          });
        }
      });
  },

  // 检查数据加载状态
  checkDataStatus() {
    // 数据状态检查方法（已移除调试输出）
  },


})